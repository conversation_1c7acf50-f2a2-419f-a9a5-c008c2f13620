import tkinter as tk
from tkinter import ttk, filedialog
import math

class Main:

    def calculate_grid(self, points, x_min, y_min, z_min, e):
        '''
        传入所有数据点，构建网格
        返回为字典，key为每个点的索引,value为其对应的格网
        '''
        m_dict = {}
        grids = set()
        for pos, point in enumerate(points):
            xx, yy, zz = point
            i = int((xx - x_min) / e)
            j = int((yy - y_min) / e)
            k = int((zz - z_min) / e)
            c_grid = (i, j, k)
            m_dict[pos] = c_grid
            grids.add(c_grid)
        t_grids = []
        for grid in grids:
            t_grids.append(grid)
        return m_dict, t_grids

    def calculate_grid_points_cnt(self, points, x_min, y_min, z_min, e):
        m_dict, grids = self.calculate_grid(points, x_min, y_min, z_min, e)
        # key为网格索引，value为列表，存储该网格内的点坐标
        m_dict2 = {i: [] for i in range(len(grids))}
        for pos, grid in enumerate(grids):
            for a, b in m_dict.items():
                if b == grid:
                    m_dict2[pos].append(points[a])
        return grids, m_dict2

    def research_near_grid(self, grid, points, x_min, y_min, z_min, e):
        '''
        计算每个点相邻格网的点，存储到near_points中
        传入一个网格[i,j,k]
        三重循环寻找格网，之后累加点数值
        '''
        grids, m_dict2 = self.calculate_grid_points_cnt(points, x_min, y_min, z_min,e)
        xx,yy,zz = grid
        near_points = []
        for i in range(-1,2):
            for j in range(-1,2):
                for k in range(-1,2):
                    # 得到小网格索引
                    if (xx+i,yy+j,zz+k) not in grids:
                        continue
                    idx = grids.index((xx+i,yy+j,zz+k))
                    near_points.extend(m_dict2[idx])
        return len(near_points), near_points

    def calculate_dis(self,point1,point2):
        x1, y1 = point1
        x2, y2 = point2
        dis = math.sqrt((x1-x2)**2 + (y1-y2)**2)
        return dis

class KDNode:
    def __init__(self,point,axis,left_points,right_points):
        self.point = point
        self.axis = axis
        self.left_points = left_points
        self.right_points = right_points

class KDTree:

    def __init__(self, points):
        self.dimension = 3
        self.root = self.build_tree(points, 0)

    def calculate_dis_s(self, point1, point2):
        x1, y1, z1 = point1
        x2, y2, z2 = point2
        dis = ((x1-x2)**2 + (y1-y2)**2 + (z1-z2)**2)
        return dis

    def build_tree(self, points, depth):
        if not points:
            return
        c_axis = depth % self.dimension
        points.sort(key=lambda x: x[c_axis])
        n = len(points)
        mid_pos = n//2
        mid_point = points[mid_pos]
        left_points = points[:mid_pos]
        right_points = points[mid_pos+1:]
        return KDNode(
            point=mid_point,
            axis=c_axis,
            left_points=self.build_tree(left_points, depth+1),
            right_points=self.build_tree(right_points, depth+1)
        )

    def k_near(self, target_point, k):
        if not self.root:
            return
        k_near_points = []

        def search(root):
            if not root:
                return
            if root.point == target_point:
                search(root.left_points)
                search(root.right_points)
                return
            dis = self.calculate_dis_s(target_point,root.point)
            if len(k_near_points) < k:
                k_near_points.append((root.point, dis))
                k_near_points.sort(key=lambda x: x[1])
            elif dis < k_near_points[-1][1]:
                k_near_points[-1] = (root.point, dis)
                k_near_points.sort(key=lambda x: x[1])
            axis = root.axis
            if target_point[axis] < root.point[axis]:
                near_node = root.left_points
                far_node = root.right_points
            else:
                far_node = root.left_points
                near_node = root.right_points
            search(near_node)
            if len(k_near_points) < k:
                search(far_node)
            if (target_point[axis]-root.point[axis])**2 < k_near_points[-1][1]:
                search(far_node)
        search(self.root)
        return [(point, math.sqrt(dis)) for point, dis in k_near_points]


class App(tk.Tk):

    def __init__(self):
        super().__init__()
        self.title("基于统计滤波的点云去噪")
        self.geometry("850x600")
        self.create_menu()
        self.create_toolbar()
        self.create_main_window()
        self.create_status_bar()
        self.config(menu=self.menu_bar)
        self.current_file = None
        self.current_font_size = None

    def create_menu(self):
        self.menu_bar = tk.Menu(self)
        # 文件菜单
        file_menu = tk.Menu(self.menu_bar, tearoff=0)
        file_menu.add_command(label="📂打开文件",command=self.open_file)
        file_menu.add_command(label="💾保存结果",command=self.save_file)
        file_menu.add_separator()
        file_menu.add_command(label="❌关闭窗体", command=self.quit)
        self.menu_bar.add_cascade(label="文件", menu=file_menu)
        # 算法菜单
        process_menu = tk.Menu(self.menu_bar, tearoff=0)
        process_menu.add_command(label="🏃运行算法", command=self.process_data)
        self.menu_bar.add_cascade(label="算法", menu=process_menu)
        # 显示菜单
        show_menu = tk.Menu(self.menu_bar,tearoff=0)
        show_menu.add_command(label="🔍放大字体", command=self.increase_font)
        show_menu.add_command(label="🔎缩小字体", command=self.decrease_font)
        self.menu_bar.add_cascade(label="显示", menu=show_menu)
    def create_toolbar(self):
        self.toolbar = ttk.Frame(self)
        # 打开文件按钮
        ptb_btn = ttk.Button(self.toolbar,text="📂打开文件", command=self.open_file)
        ptb_btn.pack(side=tk.LEFT, padx=2, pady=2)
        # 运行算法按钮
        process_btn = ttk.Button(self.toolbar, text="🏃运行算法", command=self.process_data)
        process_btn.pack(side=tk.LEFT, padx=2, pady=2)
        # 保存结果按钮
        save_btn = ttk.Button(self.toolbar, text="💾保存结果", command=self.save_file)
        save_btn.pack(side=tk.LEFT, padx=2, pady=2)
        self.toolbar.pack(side=tk.TOP, fill=tk.X)
    def create_status_bar(self):
        self.status_bar = ttk.Label(self, text="✅就绪", anchor=tk.W, background="#e8f4fd", foreground="#2c5aa0")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def open_file(self):
        try:
            file_path = filedialog.askopenfilename(filetypes=[("文本文件", "*.txt")])
            if file_path:
                self.current_file = file_path
                with open(file_path, "r", encoding="utf-8") as file:
                    content = file.read()
                    self.original_text_area.delete(1.0, tk.END)
                    self.original_text_area.insert(1.0, content)
                    self.result_text_area.config(state=tk.NORMAL)
                    self.result_text_area.delete(1.0, tk.END)
                    self.result_text_area.insert(1.0, "等待数据处理....😊")
                    self.result_text_area.config(state=tk.DISABLED)
                    self.status_bar.config(text=f"✅成功打开文件:{file_path}")
        except Exception as e:
            self.status_bar.config(text=f"❌打开文件失败:{e}")

    def save_file(self):
        file_path = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("文本文件", "*.txt")])
        if file_path:
            content = self.result_text_area.get(1.0, tk.END)
            with open(file_path, "w", encoding="utf-8") as file:
                file.write(content)
                self.status_bar.config(text=f"✅成功保存文件:{file_path}")
    def create_main_window(self):
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.data_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.data_tab, text="数据处理")
        self.paned_window = ttk.PanedWindow(self.data_tab, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        self.left_frame = ttk.LabelFrame(self.paned_window, text="原始数据")
        self.paned_window.add(self.left_frame, weight=1)
        self.right_frame = ttk.LabelFrame(self.paned_window, text="显示结果")
        self.paned_window.add(self.right_frame, weight=1)
        self.original_text_area = tk.Text(self.left_frame, wrap=tk.WORD, padx=10, pady=10, font=("Consolas",10))
        self.original_text_area.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        self.result_text_area = tk.Text(self.right_frame, wrap=tk.WORD, padx=10, pady=10, font=("Consolas", 10), state=tk.DISABLED)
        self.result_text_area.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
    def process_data(self):
        try:
            content = self.original_text_area.get(1.0, tk.END).strip()
            lines = content.split("\n")
            process_result = []
            '''
            一、读取数据文件
            '''
            x = []  # 存储x坐标
            y = []  # 存储y坐标
            z = []  # 存储z坐标
            points = []  # 存储点坐标(x,y,z)
            for line in lines:
                values = line.strip().split()
                x.append(float(values[0]))
                y.append(float(values[1]))
                z.append(float(values[2]))
                points.append([float(values[0]), float(values[1]), float(values[2])])
            process_result.append("一、读取数据文件")
            process_result.append(f"1.点P1的x坐标:{x[0]:.3f}")
            process_result.append(f"2.点P6的y坐标:{y[5]:.3f}")
            process_result.append(f"3.点P789的z坐标:{z[788]:.3f}")
            process_result.append(" ")
            process_result.append(" ")
            '''
            二、程序算法
            '''
            # 1.数据统计与初始化
            process_result.append("二、程序算法")
            n = len(points)
            process_result.append(f"1.原始点云的总点数:{int(n)}")
            # 去噪实现
            # 点云数据x,y,z最大值计算
            x_max = max(x)
            y_max = max(y)
            z_max = max(z)
            process_result.append(f"2.点云数据x最大值x_max:{x_max:.3f}")
            process_result.append(f"3.点云数据y最大值y_max:{y_max:.3f}")
            process_result.append(f"4.点云数据z最大值z_max:{z_max:.3f}")
            # 格网xyz最小值
            x_min = min(x)
            y_min = min(y)
            z_min = min(z)
            e = 3  # 记录边长为3
            # 格网xyz最小最大值
            x_max1 = (((x_max-x_min) / e) + 1) * e + x_min
            y_max1 = (((y_max-y_min) / e) + 1) * e + y_min
            z_max1 = (((z_max-z_min) / e) + 1) * e + z_min
            process_result.append(f"5.格网x_min1:{x_min:.3f}")
            process_result.append(f"6.格网x_max1:{x_max1:.3f}")
            process_result.append(f"7.格网y_min1:{y_min:.3f}")
            process_result.append(f"8.格网y_max1:{y_max1:.3f}")
            process_result.append(f"9.格网z_min1:{z_min:.3f}")
            process_result.append(f"10.格网z_max1:{z_max1:.3f}")
            # 网格(0,0,0)内的点个数
            main = Main()
            grid_dict, _ = main.calculate_grid(points, x_min, y_min, z_min, e)
            cnt = 0
            for a, b in grid_dict.items():
                if b == [0, 0, 0]:
                    cnt += 1
            process_result.append(f"11.格网(0,0,0)内的点个数:{int(cnt)}")
            # 计算分量
            process_result.append(f"12.点P1的网格索引(i,j,k)中的i分量:{int(grid_dict[0][0])}")
            process_result.append(f"13.点P6的网格索引(i,j,k)中的j分量:{int(grid_dict[5][1])}")
            # k邻近点搜索
            '''
            简单分析：
            网格是三维的
            每个网格(算上自己)一共有27个相邻网格
            现在我需要得到每个网格有多少个点，key为网格索引，value为列表，里面存储每个点
            '''
            # # 点P1的候选总点数
            # grid_1 = grid_dict[0]
            # cnt1, _ = main.research_near_grid(grid_1, points, x_min, y_min, z_min, e)
            # process_result.append(f"14.点P1的候选总点数为:{cnt1}")
            # # 点P6的候选总点数
            # grid_6 = grid_dict[5]
            # cnt6, _ = main.research_near_grid(grid_6, points, x_min, y_min, z_min, e)
            # process_result.append(f"15.点P6的候选总点数为:{cnt6}")

            # 点P1的6个邻近点序号中最大值
            main2 = KDTree(points)
            k_near_points = main2.k_near(points[0], 6)
            near_point = [point for point, _ in k_near_points]
            poss = [points.index(point)+1 for point in near_point]
            max_pos = max(poss)
            process_result.append(f"16.点P1的6个邻近点序号中最大值:{max_pos}")

            # 点P6的6个邻近点序号中最大值
            k_near_points = main2.k_near(points[5], 6)
            near_point = [point for point, _ in k_near_points]
            poss = [points.index(point) + 1 for point in near_point]
            max_pos = max(poss)
            process_result.append(f"17.点P6的6个邻近点序号中最大值:{max_pos}")

            # final
            dis_near = []
            dis_near_u = []
            for point in points:
                k_near_points = main2.k_near(point, 6)
                dis_mean = sum(dis for _, dis in k_near_points) / 6
                dis_near.append(dis_mean)
                dis_u = math.sqrt(sum((c-dis_mean)**2 for _, c in k_near_points)/6)
                dis_near_u.append(dis_u)
            process_result.append(f"18.点P1的邻域平均距离u1:{dis_near[0]:.3f}")
            process_result.append(f"19.点P1的邻域标准差:{dis_near_u[0]:.3f}")
            process_result.append(f"20.点P6的邻域平均距离u1:{dis_near[5]:.3f}")
            process_result.append(f"21.点P6的邻域标准差:{dis_near_u[5]:.3f}")
            final_mean = sum(dis_near) / len(dis_near)
            final_u = sum(dis_near_u) / len(dis_near_u)
            process_result.append(f"22.全局平均距离均值μ:{final_mean:.3f}")
            process_result.append(f"23.全局距离标准差:{final_u:.3f}")
            # 噪声判断
            flag = final_mean + 2 * final_u
            n_points = []
            b_points = []
            for pos, dis in enumerate(dis_near):
                if dis > flag:
                    n_points.append(pos)
                else:
                    b_points.append(pos)
            if 0 in n_points:
                flag1 = 1
            else:
                flag1 = 0
            process_result.append(f"24.点P1是否为噪声点:{flag1}")
            if 5 in n_points:
                flag6 = 1
            else:
                flag6 = 0
            process_result.append(f"24.点P6是否为噪声点:{flag6}")
            process_result.append(f"25.去噪的噪声点总数（输出整数）:{len(n_points)}")
            process_result.append(f"25.去噪后保留的点云总数:{len(b_points)}")
            self.result_text_area.config(state=tk.NORMAL)
            self.result_text_area.delete(1.0, tk.END)
            self.result_text_area.insert(1.0, "\n".join(process_result))
            self.result_text_area.config(state=tk.DISABLED)
            self.status_bar.config(text="✅成功处理数据")
        except Exception as e:
            self.status_bar.config(text=f"❌数据处理失败:{e}")
            self.result_text_area.config(state=tk.NORMAL)
            self.result_text_area.delete(1.0, tk.END)
            self.result_text_area.insert(1.0, f"数据处理失败❌:{e}\n请检查输入文件{self.current_file}是否符合算法规范。")
            self.result_text_area.config(state=tk.DISABLED)

    def increase_font(self):
        self.current_font_size += 1
        self.update_font()

    def decrease_font(self):
        self.current_font_size -= 1
        self.update_font()

    def update_font(self):
        c_font = ("Consolas",self.current_font_size)
        self.original_text_area.config(font=c_font)
        self.result_text_area.config(font=c_font)


if __name__ == "__main__":
    app = App()
    app.mainloop()