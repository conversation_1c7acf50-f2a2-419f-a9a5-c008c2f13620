一、读取数据文件
1.点P1的x坐标:533599.690
2.点P6的y坐标:3377146.850
3.点P789的z坐标:28.890
 
 
二、程序算法
1.原始点云的总点数:44518
2.点云数据x最大值x_max:533774.000
3.点云数据y最大值y_max:3377195.000
4.点云数据z最大值z_max:113.120
5.格网x_min1:533574.000
6.格网x_max1:533777.000
7.格网y_min1:3377000.000
8.格网y_max1:3377198.000
9.格网z_min1:8.560
10.格网z_max1:116.120
11.格网(0,0,0)内的点个数:0
12.点P1的网格索引(i,j,k)中的i分量:8
13.点P6的网格索引(i,j,k)中的j分量:48
14.点P1的候选总点数为:70
15.点P6的候选总点数为:48
16.点P1的6个邻近点序号中最大值:396
17.点P6的6个邻近点序号中最大值:494
18.点P1的邻域平均距离u1:1.885
19.点P1的邻域标准差:0.505
20.点P6的邻域平均距离u1:2.441
21.点P6的邻域标准差:0.278
22.全局平均距离均值μ:1.668
23.全局距离标准差:0.339
24.点P1是否为噪声点:0
24.点P6是否为噪声点:1
25.去噪的噪声点总数（输出整数）:1973
25.去噪后保留的点云总数:42545
